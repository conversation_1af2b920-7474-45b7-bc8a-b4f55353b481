import { Canvas } from "fabric";
import { CropData } from "@/shared/types";

export const calculateFittedCanvasDimensions = (
  contentWidth: number,
  contentHeight: number,
  containerWidth: number,
  containerHeight: number
) => {
  const aspectRatio = contentWidth / contentHeight;
  let targetWidth = containerWidth;
  let targetHeight = targetWidth / aspectRatio;

  if (targetHeight > containerHeight) {
    targetHeight = containerHeight;
    targetWidth = targetHeight * aspectRatio;
  }

  return { width: targetWidth, height: targetHeight };
};

export const shouldResize = (
  currentWidth: number,
  currentHeight: number,
  targetWidth: number,
  targetHeight: number,
  threshold: number = 5
): boolean => {
  return (
    Math.abs(currentWidth - targetWidth) >= threshold ||
    Math.abs(currentHeight - targetHeight) >= threshold
  );
};

export const scaleCanvasObjects = (canvas: Canvas, imageScale: number) => {
  canvas.forEachObject((obj) => {
    const objName = (obj as unknown as Record<string, unknown>)?.name;
    if (objName !== "backgroundImage") {
      obj.set({
        scaleX: (obj.scaleX || 1) * imageScale,
        scaleY: (obj.scaleY || 1) * imageScale,
        left: (obj.left || 0) * imageScale,
        top: (obj.top || 0) * imageScale,
      });

      if ("strokeUniform" in obj) {
        obj.strokeUniform = true;
      }

      if (obj.type === "textbox" || obj.type === "text") {
        const textbox = obj as any;
        textbox.fontSize = 16;
        textbox.set({
          scaleX: 1,
          scaleY: 1,
          lockScalingX: true,
          lockScalingY: true,
          hasControls: false,
        });
      }

      obj.setCoords();
    }
  });
};

export const updateCropDataDimensions = (
  cropData: CropData,
  containerWidth: number,
  containerHeight: number
): CropData => {
  return {
    ...cropData,
    canvasDimensions: {
      width: containerWidth,
      height: containerHeight,
    },
  };
};
