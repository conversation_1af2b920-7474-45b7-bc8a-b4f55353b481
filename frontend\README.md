# Frontend Code Architecture

Medical imaging application frontend with React/TypeScript, Fabric.js canvas manipulation, and Cornerstone.js DICOM viewing.

## 📁 Code Organization

```
frontend/src/
├── components/              # UI Components
│   ├── toolbars/           # Toolbar components
│   │   ├── FabricToolbar.tsx
│   │   └── components/     # Toolbar sub-components
│   └── viewers/            # Image viewer components
│       ├── ImageViewer.tsx
│       ├── StackViewer.tsx
│       └── VolumeViewer.tsx
├── hooks/                  # Custom React hooks
├── lib/                    # Core functionality libraries
├── shared/                 # Types and utilities
├── layouts/                # Page layouts
└── pages/                  # Route components
```

## 🎯 Core Components

### `/src/components/viewers/`

**`ImageViewer.tsx`** - 2D image viewer with Fabric.js canvas

- Integrates `useFabricViewer` hook for canvas management
- Passes current filter state to `FabricToolbar` (not backend values)
- Handles responsive canvas resizing with `useResponsiveCanvas`
- Manages grouped handler props pattern for cleaner component interface

**`StackViewer.tsx`** - DICOM stack viewer with Cornerstone.js

- Combines Cornerstone.js viewport with Fabric.js overlay canvas
- Initializes DICOM rendering engine and viewport setup
- Same filter state management pattern as ImageViewer
- Handles DICOM-specific initialization and stack loading

**`VolumeViewer.tsx`** - 3D volume rendering

- Uses Cornerstone.js volume viewport for 3D rendering
- Handles volume shift controls and 3D navigation
- Independent of Fabric.js canvas (no annotations)

### `/src/components/toolbars/`

**`FabricToolbar.tsx`** - Main toolbar component

- Receives `fabricConfigs` with current filter values (not backend defaults)
- Uses `config` prop for feature toggling (`disableGrayscale`, `disableGamma`)
- Manages calibration modal state and localStorage integration
- Implements grouped handlers pattern: `filter`, `transform`, `actions`, `tracking`
- Handles calibration data priority: localStorage > backend > defaults

**`/components/` sub-components:**

- `SliderControls.tsx` - Brightness/contrast/sharpness range sliders
- `GammaControls.tsx` - RGB gamma adjustment sliders with color coding
- `TransformControls.tsx` - Rotation, flip, grayscale, invert toggle buttons
- `ToolGrid.tsx` - Drawing tools (select, freehand, shapes, crop, measure)
- `ActionButtons.tsx` - Undo, save, show original action buttons
- `CalibrationModal.tsx` - Calibration setup modal for measurements

## 🔧 Custom Hooks Architecture

### `/src/hooks/`

**`useFabricViewer.ts`** - Main orchestration hook

- Combines all sub-hooks: filters, transforms, crop, undo tracking
- Returns current filter state values (not backend defaults)
- Manages canvas setup and event listeners
- Creates save/show original handlers
- Removed all frontend defaults - backend handles all defaults

**`useFilterManagement.ts`** - Image filter state management

- Manages brightness, contrast, sharpness, gamma RGB, grayscale, invert
- Uses `useState` for current filter values (no useEffect sync)
- Creates filter change handlers that update canvas and state
- Returns current values and handlers separately

**`useImageTransforms.ts`** - Image rotation and flipping

- Manages transform state: rotations, flipHorizontal, flipVertical
- Handles 90-degree rotation increments with canvas transforms
- Applies transforms using Fabric.js canvas manipulation
- Preserves annotations during transforms

**`useCropManagement.ts`** - Image cropping functionality

- Manages crop state and canvas dimension changes
- Handles crop rectangle creation and application
- Preserves annotations by saving/removing/reapplying during crop
- Updates canvas size to match cropped dimensions

**`useUndoTracking.ts`** - Undo/redo operations

- Tracks canvas state changes for undo functionality
- Manages undo stack with different action types
- Handles filter, transform, annotation, and crop undo actions
- Uses refs to prevent infinite re-renders during undo

**`useFabricTools.ts`** - Drawing tools management

- Manages active tool mode (select, freehand, shapes, etc.)
- Handles tool-specific canvas event listeners
- Creates shape objects with proper styling and properties
- Manages calibration and measurement tools

**`useResponsiveCanvas.ts`** - Canvas responsive resizing

- Handles canvas resizing on container dimension changes
- Uses ResizeObserver for smooth resize detection
- Maintains aspect ratio during resize operations
- Handles both image and annotation scaling

## 📚 Core Libraries

### `/src/lib/fabric/`

**`operations/`** - Core Fabric.js operations

- `annotations.ts` - Load/save annotations from backend, apply to canvas
- `crop.ts` - Crop image with annotation preservation, resize canvas
- `filters.ts` - Apply image filters, create filter handlers, local defaults
- `measurements.ts` - Distance measurement with calibration data
- `save.ts` - Save current state to backend (filters, annotations, transforms)
- `showOriginal.ts` - Reset to original image state, local defaults
- `transforms.ts` - Rotation/flip operations with annotation preservation
- `undo.ts` - Undo stack management, restore previous canvas states
- `calibration.ts` - Calibration data management, localStorage integration

**`tools/`** - Drawing tools and interactions

- `drawingInteractions.ts` - Mouse event handlers for shape drawing
- `shapeCreators.ts` - Create Fabric.js objects (rect, circle, line, text)
- `toolConfigs.ts` - Tool-specific configurations and constraints
- `toolDefinitions.ts` - Available drawing tools with icons and modes
- `toolModeManager.ts` - Tool mode switching and canvas cursor management

**`events/`** - Canvas event handling

- `canvasEventListeners.ts` - Object tracking for undo, measurement events

**`canvas/`** - Canvas setup and management

- `setup.ts` - Initialize Fabric.js canvas, load image, apply configurations

**`rendering/`** - Image rendering utilities

- `image.ts` - Image loading with Fabric.js, background image setup
- `resize.ts` - Canvas responsive resizing, aspect ratio maintenance

### `/src/lib/dicom/`

**`core/`** - Core DICOM functionality

- `index.ts` - Initialize Cornerstone.js, setup rendering engine
- `viewport.ts` - Create and manage DICOM viewports
- `stack.ts` - Load DICOM image stacks, handle stack navigation

**`config/`** - DICOM tools configuration

- `dicomToolsConfig.ts` - Tool bindings, orientation markers, mouse controls

**`handlers/`** - DICOM event handlers

- `volumeEventHandlers.ts` - Volume-specific event handling

**`utils/`** - DICOM utilities

- `volumeUtils.ts` - Volume loading and manipulation utilities

## 🗂️ Type Definitions

### `/src/shared/types/`

**`medicalImageTypes.ts`** - Core type definitions

- `FabricConfig` - Image filter and annotation configuration
- `ImageToolbarProps` - Toolbar component props with grouped handlers
- `FilterParams` - Image filter parameters (brightness, contrast, etc.)
- `CalibrationData` - Measurement calibration data structure
- `CropData` - Image crop state and dimensions
- `TransformState` - Image rotation and flip state
- `UndoAction` - Undo operation type definitions
- `ToolMode` - Drawing tool mode enumeration

**`index.ts`** - Type exports for clean imports throughout application

## 🏗️ Data Flow Patterns

### Filter State Management

1. **Backend** provides initial `fabricConfigs` with default values
2. **useFilterManagement** manages current filter state with `useState`
3. **Viewers** destructure current values from `useFabricViewer`
4. **FabricToolbar** receives current values (not backend defaults)
5. **Sliders** display current state, handlers update both canvas and state

### Handler Grouping Pattern

```typescript
handlers={{
  filter: { handleBrightnessChange, handleContrastChange, ... },
  transform: { handleRotate, handleFlipHorizontal, ... },
  actions: { handleUndo, handleSave, handleCrop, ... },
  tracking: { disableUndoTracking, enableUndoTracking }
}}
```

### Configuration Priority

1. **localStorage** (temporary calibration data)
2. **Backend data** (saved configurations)
3. **No frontend defaults** (backend handles all defaults)

## 🏛️ Architecture Patterns

### Hook Composition Strategy

- **Main Hook** (`useFabricViewer`) orchestrates specialized sub-hooks
- **Single Responsibility** - each hook manages one concern (filters, transforms, etc.)
- **Clean Returns** - hooks return both current state and handlers
- **No Frontend Defaults** - all defaults handled by backend only

### Component Data Flow

- **Props Drilling Avoided** - grouped handlers pattern reduces prop complexity
- **Current State Priority** - UI displays current filter state, not backend defaults
- **Responsive Updates** - filter changes immediately update canvas and sliders

### State Management Patterns

- **useState for Current State** - no useEffect synchronization needed
- **Refs for Performance** - prevent re-renders during undo operations
- **Local State for Temporary Data** - calibration data in localStorage
- **Backend State for Persistence** - saved configurations and defaults

### Error Handling

- **Non-null Assertions** - backend data assumed valid (no frontend fallbacks)
- **Conditional Rendering** - UI disabled during "show original" mode
- **Graceful Degradation** - missing calibration data handled with prompts

### Performance Optimizations

- **ResizeObserver** - smooth canvas resizing without debouncing
- **Event Listener Management** - proper cleanup in useEffect hooks
- **Canvas State Preservation** - annotations maintained during transforms/crops
- **Minimal Re-renders** - careful dependency arrays in hooks
