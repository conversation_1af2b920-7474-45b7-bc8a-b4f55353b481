import React from "react";
import { Fa<PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON><PERSON>, FaSearchPlus } from "react-icons/fa";
import type { SliderControlsProps } from "@/shared/types";

const SliderControls: React.FC<SliderControlsProps> = ({
  brightness,
  contrast,
  sharpness,
  isShowingOriginal,
  onBrightnessChange,
  onContrastChange,
  onSharpnessChange,
}) => {
  const sliders = [
    {
      icon: FaSun,
      label: "Bright",
      value: brightness,
      onChange: onBrightnessChange,
      min: 0.1,
      max: 2,
      step: 0.1,
    },
    {
      icon: FaAdjust,
      label: "Contrast",
      value: contrast,
      onChange: onContrastChange,
      min: 0.1,
      max: 2,
      step: 0.1,
    },
    {
      icon: FaSearchPlus,
      label: "Sharpness",
      value: sharpness,
      onChange: onSharpnessChange,
      min: 0.1,
      max: 2,
      step: 0.1,
    },
  ];

  return (
    <>
      {sliders.map((slider) => {
        const IconComponent = slider.icon;
        return (
          <div key={slider.label} className="control-item">
            <div className="control-icon" title={slider.label}>
              <IconComponent />
            </div>
            <input
              type="range"
              min={slider.min}
              max={slider.max}
              step={slider.step}
              value={slider.value}
              onChange={(e) => !isShowingOriginal && slider.onChange(parseFloat(e.target.value))}
              className="horizontal-slider"
              title={`${slider.label}: ${(slider.value || 0).toFixed(1)}`}
              disabled={isShowingOriginal}
            />
            <span>{(slider.value || 0).toFixed(1)}</span>
          </div>
        );
      })}
    </>
  );
};

export default SliderControls;
