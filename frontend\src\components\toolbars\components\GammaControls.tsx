import React from "react";
import { FaGem } from "react-icons/fa";
import type { GammaControlsProps } from "@/shared/types";

const GammaControls: React.FC<GammaControlsProps> = ({
  gammaR,
  gammaG,
  gammaB,
  disableGamma,
  isShowingOriginal,
  onGammaRChange,
  onGammaGChange,
  onGammaBChange,
}) => {
  const gammaSliders = [
    {
      label: "R",
      value: gammaR,
      onChange: onGammaRChange,
      className: "gamma-red",
    },
    {
      label: "G",
      value: gammaG,
      onChange: onGammaGChange,
      className: "gamma-green",
    },
    {
      label: "B",
      value: gammaB,
      onChange: onGammaBChange,
      className: "gamma-blue",
    },
  ];

  return (
    <div className={`gamma-controls ${disableGamma || isShowingOriginal ? "disabled" : ""}`}>
      <div className="gamma-icon" title="Gamma RGB">
        <FaGem />
      </div>
      <div className="gamma-sliders">
        {gammaSliders.map((gamma) => (
          <div key={gamma.label} className="gamma-slider-item">
            <div className={`gamma-label ${gamma.label.toLowerCase()}`}>{gamma.label}</div>
            <input
              type="range"
              min="1"
              max="2.2"
              step="0.05"
              value={gamma.value}
              onChange={(e) =>
                !disableGamma && !isShowingOriginal && gamma.onChange(parseFloat(e.target.value))
              }
              className={`horizontal-slider ${gamma.className}`}
              disabled={disableGamma || isShowingOriginal}
              title={`Gamma ${gamma.label}: ${(gamma.value || 1).toFixed(2)}`}
            />
            <span className="gamma-value">{(gamma.value || 1).toFixed(2)}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default GammaControls;
