import { Canvas, Textbox, Group } from "fabric";
import { ToolMode, FabricMeasurementLine, CalibrationData } from "@/shared/types";
import { constrainToCan<PERSON>, transformPointer } from "./toolConfigs";
import { createInitialShape, updateShapeSize } from "./shapeCreators";
import { updateMeasurementText } from "@/lib/fabric/operations/measurements";

export const handleTextToolClick = (
  pointer: { x: number; y: number },
  canvas: Canvas,
  onShapeCreated?: () => void
) => {
  // Transform pointer coordinates first to handle flipped canvas
  const transformedPointer = transformPointer(pointer, canvas);

  const objects = canvas.getObjects();
  const clickedObject = objects.find((obj) => {
    // Check for textbox directly or textbox within a group
    if (obj.type === "textbox") {
      const objBounds = obj.getBoundingRect();
      return (
        transformedPointer.x >= objBounds.left &&
        transformedPointer.x <= objBounds.left + objBounds.width &&
        transformedPointer.y >= objBounds.top &&
        transformedPointer.y <= objBounds.top + objBounds.height
      );
    } else if (obj.type === "group") {
      const group = obj as Group;
      const textboxInGroup = group.getObjects().find((groupObj) => groupObj.type === "textbox");
      if (textboxInGroup) {
        const objBounds = obj.getBoundingRect();
        return (
          transformedPointer.x >= objBounds.left &&
          transformedPointer.x <= objBounds.left + objBounds.width &&
          transformedPointer.y >= objBounds.top &&
          transformedPointer.y <= objBounds.top + objBounds.height
        );
      }
    }
    return false;
  });

  if (clickedObject) {
    canvas.setActiveObject(clickedObject);

    // Handle textbox editing for both direct textbox and grouped textbox
    if (clickedObject.type === "textbox") {
      (clickedObject as Textbox).enterEditing();
    } else if (clickedObject.type === "group") {
      const group = clickedObject as Group;
      const textboxInGroup = group.getObjects().find((obj) => obj.type === "textbox") as Textbox;
      if (textboxInGroup) {
        textboxInGroup.enterEditing();
      }
    }
    return true;
  }

  const shape = createInitialShape("text", transformedPointer, canvas);
  if (!shape) return false;

  canvas.add(shape);
  canvas.setActiveObject(shape);

  // Handle the grouped textbox
  if (shape.type === "group") {
    const group = shape as Group;
    const textboxInGroup = group.getObjects().find((obj) => obj.type === "textbox") as Textbox;
    if (textboxInGroup) {
      textboxInGroup.enterEditing();
      textboxInGroup.selectAll();
    }
  }

  if (onShapeCreated) onShapeCreated();
  return true;
};

export const startDrawingShape = (
  pointer: { x: number; y: number },
  activeMode: ToolMode,
  canvas: Canvas,
  isShowingOriginal: boolean,
  hasPerformedCrop: boolean,
  onShapeCreated?: () => void
) => {
  if (!activeMode || activeMode === "select" || isShowingOriginal) {
    return null;
  }

  if (activeMode === "crop" && hasPerformedCrop) {
    return null;
  }

  const constrainedPointer = constrainToCanvas(pointer, canvas);

  if (activeMode === "text") {
    handleTextToolClick(constrainedPointer, canvas, onShapeCreated);
    return null;
  }

  const transformedPointer = transformPointer(constrainedPointer, canvas);
  const shape = createInitialShape(activeMode, transformedPointer, canvas);
  if (!shape) return null;

  canvas.add(shape);
  return { shape, startPoint: transformedPointer };
};

export const updateDrawingShape = (
  pointer: { x: number; y: number },
  activeMode: ToolMode,
  canvas: Canvas,
  currentShape: any,
  startPoint: { x: number; y: number },
  isShowingOriginal: boolean,
  disableUndoTracking?: () => void,
  enableUndoTracking?: () => void,
  calibrationData?: CalibrationData
) => {
  if (!currentShape || !startPoint || isShowingOriginal) return;

  const constrainedPointer = constrainToCanvas(pointer, canvas);
  const transformedPointer = transformPointer(constrainedPointer, canvas);

  updateShapeSize(currentShape, startPoint, transformedPointer, activeMode, canvas);

  if (activeMode === "measure" && calibrationData) {
    const line = currentShape as FabricMeasurementLine;
    if (line.measurementText) {
      disableUndoTracking?.();
      canvas.remove(line.measurementText);
      enableUndoTracking?.();
      line.measurementText = undefined;
    }
    disableUndoTracking?.();
    updateMeasurementText(canvas, line, calibrationData);
    enableUndoTracking?.();
  }
  canvas.renderAll();
};

export const finishDrawingShape = (
  activeMode: ToolMode,
  currentShape: any,
  canvas: Canvas,
  isShowingOriginal: boolean,
  onCrop?: () => void,
  onShapeCreated?: () => void,
  disableUndoTracking?: () => void,
  enableUndoTracking?: () => void,
  calibrationData?: CalibrationData
) => {
  if (!currentShape || isShowingOriginal) return;

  if (activeMode === "crop" && onCrop) onCrop();

  if (activeMode === "measure" && calibrationData) {
    const line = currentShape as FabricMeasurementLine;
    if (!line.measurementText) {
      disableUndoTracking?.();
      updateMeasurementText(canvas, line, calibrationData);
      enableUndoTracking?.();
    }
  }

  if (onShapeCreated && activeMode !== "crop") onShapeCreated();
};
