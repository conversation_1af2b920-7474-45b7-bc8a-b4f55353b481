import { useState } from "react";
import { Canvas } from "fabric";
import { TransformState, ImageTransformsState } from "@/shared/types";
import {
  createRotateHandler,
  createFlipHorizontalHandler,
  createFlipVerticalHandler,
} from "@/lib/fabric/operations";
export const useImageTransforms = (
  fabricCanvas: React.RefObject<Canvas | null>,
  initialTransformState: TransformState,
  onRotationComplete?: () => void
): ImageTransformsState => {
  const [transformState, setTransformState] = useState<TransformState>(initialTransformState);

  const getCurrentTransformState = () => transformState;

  const handleRotate = createRotateHandler(fabricCanvas, setTransformState, onRotationComplete);
  const handleFlipHorizontal = createFlipHorizontalHandler(
    fabricCanvas,
    setTransformState,
    getCurrentTransformState
  );
  const handleFlipVertical = createFlipVerticalHandler(
    fabricCanvas,
    setTransformState,
    getCurrentTransformState
  );

  return {
    transformState,
    setTransformState,
    handleRotate,
    handleFlipHorizontal,
    handleFlipVertical,
  };
};
