import { Canvas, Group, Textbox } from "fabric";

export const setToolMode = (mode: string, canvas: Canvas) => {
  canvas.selection = false;
  canvas.discardActiveObject();
  canvas.forEachObject((obj) => {
    const objName = (obj as unknown as Record<string, unknown>)?.name;
    if (objName !== "backgroundImage") {
      obj.selectable = false;
      obj.evented = false;
    }
  });
  canvas.defaultCursor = "crosshair";
  canvas.isDrawingMode = false;
  canvas.selection = false;

  if (mode === "freehand") {
    canvas.isDrawingMode = true;
  } else if (mode === "select") {
    canvas.defaultCursor = "default";
    canvas.isDrawingMode = false;
    canvas.selection = true;

    canvas.forEachObject((obj) => {
      const objName = (obj as unknown as Record<string, unknown>)?.name;
      if (objName !== "backgroundImage") {
        obj.selectable = true;
        obj.evented = true;
        obj.hasControls = true;
        obj.hasBorders = true;
        obj.setCoords();

        // Enable editing for textboxes within groups
        if (obj.type === "group") {
          const group = obj as Group;
          const textboxInGroup = group
            .getObjects()
            .find((groupObj) => groupObj.type === "textbox") as Textbox;
          if (textboxInGroup) {
            textboxInGroup.editable = true;
          }
        }
      }
    });
  }
  canvas.renderAll();
};
