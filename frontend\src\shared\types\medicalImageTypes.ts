import { Enums, Types } from "@cornerstonejs/core";
import { Canvas, Line, FabricText } from "fabric";

export interface ApiResponse<T = unknown> {
  success: boolean;
  message: string;
  data: T;
}

export interface ImageData {
  id: string;
  viewer: {
    imageUrl: string;
    fabricConfigs: FabricConfig;
  };
}

export interface VolumeData {
  id: string;
  viewer: {
    imageUrl: string[];
    configs: VolumeConfig;
  };
}

export interface VolumeViewerProps {
  data: VolumeData;
}

export interface ImageViewerProps {
  data: ImageData;
  onShapeCreated?: () => void;
}

export interface CropRect {
  left: number;
  top: number;
  width: number;
  height: number;
}

export interface CropData {
  isCropped: boolean;
  canvasDimensions?: { width: number; height: number };
  normalizedCropRect?: {
    left: number;
    top: number;
    width: number;
    height: number;
  };
  transformStateAtCrop?: TransformState;
}

export interface CalibrationData {
  calibrationDistance: number;
  calibratedPixelLength: number;
  calibrationImageScale: number;
}

export interface FabricConfig {
  contrast: number;
  brightness: number;
  grayscale: boolean;
  invert: boolean;
  sharpness: number;
  gammaR: number;
  gammaG: number;
  gammaB: number;
  annotations: Record<string, unknown>;
  cropData?: CropData;
  transformState?: TransformState;
  calibrationData?: CalibrationData;
}

export interface VolumeConfig {
  shift: number;
}
export interface FileItem {
  id: string;
  name: string;
  type: "stack" | "volume" | "image";
}

export interface PatientDetails {
  patientId: string;
  patientName: string;
  files: FileItem[];
}

export interface ImageToolbarProps {
  fabricCanvas: React.MutableRefObject<Canvas | null>;
  fabricConfigs: FabricConfig;
  handlers: {
    filter: {
      handleBrightnessChange: (value: number) => void;
      handleContrastChange: (value: number) => void;
      handleGrayscaleChange: (value: boolean) => void;
      handleInvertChange: (value: boolean) => void;
      handleSharpnessChange: (value: number) => void;
      handleGammaRChange: (value: number) => void;
      handleGammaGChange: (value: number) => void;
      handleGammaBChange: (value: number) => void;
    };
    transform: {
      handleRotate: () => void;
      handleFlipHorizontal: () => void;
      handleFlipVertical: () => void;
    };
    actions: {
      handleUndo: () => void;
      handleSave: () => void;
      handleShowOriginal: () => void;
      handleCrop: () => void;
    };
    tracking: {
      disableUndoTracking: () => void;
      enableUndoTracking: () => void;
    };
  };
  state: {
    canUndo: boolean;
    isShowingOriginal: boolean;
    hasPerformedCrop: boolean;
  };
  config: {
    disableGrayscale?: boolean;
    disableGamma?: boolean;
  };
  onShapeCreated?: () => void;
}

export interface FilterParams {
  brightness: number;
  contrast: number;
  grayscale: boolean;
  invert: boolean;
  sharpness: number;
  gammaR: number;
  gammaG: number;
  gammaB: number;
}

export type PartialFilterParams = Partial<FilterParams>;

export interface FabricMeasurementLine extends Line {
  measurementText?: FabricText;
}

export type UndoActionType =
  | "add"
  | "remove"
  | "modify"
  | "filter"
  | "rotate"
  | "flipH"
  | "flipV"
  | "crop";

export interface AddUndoAction {
  type: "add";
  objectCount: number;
}
export interface MeasurementUndoAction {
  type: "add-measurement";
  lineId: string;
}
export interface RemoveUndoAction {
  type: "remove";
  objectCount: number;
}

export interface FabricObjectState {
  left: number;
  top: number;
  scaleX: number;
  scaleY: number;
  angle: number;
}

export interface ModifyUndoAction {
  type: "modify";
  objectId: string;
  previousState: FabricObjectState;
}

export interface CropUndoAction {
  type: "crop";
  previousUndoStack?: UndoAction[];
  originalViewportTransform?: number[];
  originalCanvasDimensions?: { width: number; height: number };
}

export type UndoAction =
  | AddUndoAction
  | RemoveUndoAction
  | ModifyUndoAction
  | CropUndoAction
  | MeasurementUndoAction;

export type ToolMode =
  | "select"
  | "freehand"
  | "text"
  | "rect"
  | "line"
  | "circle"
  | "crop"
  | "calibrate"
  | "measure";
export interface ViewportConfig {
  viewportId: string;
  type: Enums.ViewportType;
  element: HTMLDivElement;
  background?: Types.Point3;
  orientation?: Enums.OrientationAxis;
}
export interface TransformState {
  rotations: number;
  flipHorizontal: boolean;
  flipVertical: boolean;
}

export interface UseResponsiveCanvasProps {
  fabricCanvas: React.RefObject<Canvas>;
  containerRef: React.RefObject<HTMLElement | null>;
  cropData?: CropData;
  setCropData?: (data: CropData) => void;
}

export interface SetupCanvasParams {
  canvasElement: HTMLCanvasElement;
  imageUrl: string;
  annotations?: unknown;
  filters?: FilterParams;
  cropData?: CropData;
  existingCanvas?: Canvas | null;
  transformState?: TransformState;
}

export interface LoadImageOptions {
  containerRect?: DOMRect;
}

export type ToolClass =
  | typeof import("@cornerstonejs/tools").ZoomTool
  | typeof import("@cornerstonejs/tools").PanTool
  | typeof import("@cornerstonejs/tools").StackScrollTool
  | typeof import("@cornerstonejs/tools").TrackballRotateTool
  | typeof import("@cornerstonejs/tools").WindowLevelTool;

export type VolumeToolClass =
  | ToolClass
  | typeof import("@cornerstonejs/tools").OrientationMarkerTool;

export interface ToolBinding {
  tool: ToolClass;
  bindings: { mouseButton: number }[];
}

export interface VolumeToolBinding {
  tool: VolumeToolClass;
  bindings: { mouseButton: number }[];
}

export interface ViewerConfig {
  tools: ToolClass[];
  bindings: ToolBinding[];
}

export interface VolumeViewerConfig {
  tools: VolumeToolClass[];
  bindings: VolumeToolBinding[];
}

export interface UseFabricToolsProps {
  fabricCanvas: React.MutableRefObject<Canvas | null>;
  isShowingOriginal?: boolean;
  hasPerformedCrop?: boolean;
  onShapeCreated?: () => void;
  onCrop?: () => void;
  disableUndoTracking?: () => void;
  enableUndoTracking?: () => void;
  showCalibrationModal?: () => void;
  calibrationData?: CalibrationData;
  onCalibrationPrompt?: () => void;
}

export interface UndoTrackingState {
  undoStack: UndoAction[];
  canUndo: boolean;
  isUndoingRef: React.MutableRefObject<boolean>;
  handleUndo: () => Promise<void>;
  addUndoAction: (action: UndoAction) => void;
  disableUndoTracking: () => void;
  enableUndoTracking: () => void;
}

export interface ImageTransformsState {
  transformState: TransformState;
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>;
  handleRotate: () => void;
  handleFlipHorizontal: () => void;
  handleFlipVertical: () => void;
}

export interface CropManagementState {
  cropData: CropData;
  setCropData: React.Dispatch<React.SetStateAction<CropData>>;
  hasPerformedCrop: boolean;
  setHasPerformedCrop: React.Dispatch<React.SetStateAction<boolean>>;
  handleCrop: () => Promise<void>;
}

export interface ToolDefinition {
  icon: React.ComponentType;
  title: string;
  mode: ToolMode;
}

// Toolbar and Controls Props
export interface ActionButtonsProps {
  canUndo: boolean;
  isShowingOriginal: boolean;
  onUndo?: () => void;
  onSave?: () => void;
}

export interface SliderControlsProps {
  brightness: number;
  contrast: number;
  sharpness: number;
  isShowingOriginal: boolean;
  onBrightnessChange: (value: number) => void;
  onContrastChange: (value: number) => void;
  onSharpnessChange: (value: number) => void;
}

export interface TransformControlsProps {
  isShowingOriginal: boolean;
  grayscale: boolean;
  invert: boolean;
  disableGrayscale: boolean;
  onRotate?: () => void;
  onFlipHorizontal?: () => void;
  onFlipVertical?: () => void;
  onGrayscaleChange: (value: boolean) => void;
  onInvertChange: (value: boolean) => void;
  onShowOriginal?: () => void;
}

export interface GammaControlsProps {
  gammaR: number;
  gammaG: number;
  gammaB: number;
  disableGamma: boolean;
  isShowingOriginal: boolean;
  onGammaRChange: (value: number) => void;
  onGammaGChange: (value: number) => void;
  onGammaBChange: (value: number) => void;
}

export interface ToolGridProps {
  activeMode: ToolMode | null;
  isShowingOriginal: boolean;
  hasPerformedCrop: boolean;
  onToolSelect: (mode: ToolMode) => void;
  onCrop?: () => void;
}

// Filter Management Types
export interface FilterState {
  brightness: number;
  contrast: number;
  grayscale: boolean;
  invert: boolean;
  sharpness: number;
  gammaR: number;
  gammaG: number;
  gammaB: number;
}

export interface FilterHandlers {
  handleBrightnessChange: (value: number) => void;
  handleContrastChange: (value: number) => void;
  handleGrayscaleChange: (value: boolean) => void;
  handleInvertChange: (value: boolean) => void;
  handleSharpnessChange: (value: number) => void;
  handleGammaRChange: (value: number) => void;
  handleGammaGChange: (value: number) => void;
  handleGammaBChange: (value: number) => void;
}

export interface FilterManagementState {
  filters: FilterState;
  filterHandlers: FilterHandlers;
  updateFilter: (keyOrConfig: string | object, value?: number | boolean) => void;
}
