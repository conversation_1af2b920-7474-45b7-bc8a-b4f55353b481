import { Canvas } from "fabric";
import { saveFabricConfig } from "@/shared/api";
import { CropData, FabricConfig, TransformState, CalibrationData } from "@/shared/types";
import { getCalibrationFromLocalStorage, clearCalibrationFromLocalStorage } from "./calibration";

export const createSaveHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  dataId: string,
  filters: Record<string, any>,
  canvasCropData: CropData,
  transformState: TransformState,
  calibrationData?: CalibrationData
) => {
  return async () => {
    if (fabricCanvas?.current) {
      const annotations = fabricCanvas.current.toJSON();
      annotations.canvasWidth = fabricCanvas.current.getWidth();
      annotations.canvasHeight = fabricCanvas.current.getHeight();

      // Always use localStorage calibration data for save
      const finalCalibrationData = getCalibrationFromLocalStorage();

      const fabricConfig: FabricConfig = {
        brightness: filters.brightness,
        contrast: filters.contrast,
        grayscale: filters.grayscale,
        invert: filters.invert,
        sharpness: filters.sharpness,
        gammaR: filters.gammaR,
        gammaG: filters.gammaG,
        gammaB: filters.gammaB,
        annotations,
        cropData: canvasCropData,
        transformState,
        ...(finalCalibrationData && { calibrationData: finalCalibrationData }),
      };

      await saveFabricConfig(dataId, fabricConfig);
      // Clear localStorage calibration data after successful save
      if (finalCalibrationData && !calibrationData) {
        // Only clear if we used localStorage data (not backend data)
        clearCalibrationFromLocalStorage();
      }
    }
  };
};
