import { Canvas } from "fabric";
import { updateMeasurementOnModify, isMeasurementLine } from "../operations/measurements";
import {
  FabricMeasurementLine,
  FabricObjectState,
  UndoAction,
  UndoTrackingState,
} from "@/shared/types";

export const createUndoTrackingAddHandler = (undoTracking: UndoTrackingState, canvas: Canvas) => {
  return (e?: any) => {
    if (!undoTracking.isUndoingRef.current) {
      const addedObject = e?.path || e?.target;
      if (addedObject) {
        const objName = (addedObject as any).name;
        if (objName === "measurementLine") {
          undoTracking.addUndoAction({
            type: "add-measurement",
            lineId: addedObject?.id,
          } as UndoAction);
        } else if (
          !objName ||
          (!objName.includes("background") &&
            objName !== "cropRect" &&
            objName !== "measurementText" &&
            objName !== "calibrateLine")
        ) {
          undoTracking.addUndoAction({
            type: "add",
            objectCount: canvas.getObjects().length,
          } as UndoAction);
        }
      }
    }
  };
};

export const createUndoTrackingRemoveHandler = (undoTracking: UndoTrackingState) => {
  return (e?: any) => {
    if (!undoTracking.isUndoingRef.current) {
      const removedObject = e?.path || e?.target;
      const objName = (removedObject as any).name;
      if (objName !== "calibrateLine") {
        undoTracking.addUndoAction({
          type: "remove",
          objectCount: 0,
        });
      }
    }
  };
};

export const createUndoTrackingModifyHandler = (
  undoTracking: UndoTrackingState,
  objectStates: React.MutableRefObject<Map<string, FabricObjectState>>
) => {
  return (e: { target: unknown }) => {
    if (
      undoTracking.isUndoingRef.current ||
      !e.target ||
      (e.target as any)?.name === "measurementText"
    )
      return;

    const obj = e.target as any;
    const objId = obj.id as string;
    const previousState = objectStates.current.get(objId);
    if (previousState) {
      undoTracking.addUndoAction({ type: "modify", objectId: objId, previousState });
      objectStates.current.delete(objId);
    }
  };
};

export const createObjectStateTrackingHandler = (
  undoTracking: UndoTrackingState,
  objectStates: React.MutableRefObject<Map<string, FabricObjectState>>
) => {
  return (e: { target: unknown }) => {
    if (
      undoTracking.isUndoingRef.current ||
      !e.target ||
      isMeasurementLine(e.target) ||
      (e.target as any)?.name === "measurementText"
    )
      return;

    const obj = e.target as any;
    let objId = obj.id as string;
    if (!objId) {
      objId = `obj_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      obj.id = objId;
    }
    if (!objectStates.current.has(objId)) {
      objectStates.current.set(objId, {
        left: obj.left || 0,
        top: obj.top || 0,
        scaleX: obj.scaleX || 1,
        scaleY: obj.scaleY || 1,
        angle: obj.angle || 0,
      });
    }
  };
};

export const createMeasurementModificationHandler = (
  canvas: Canvas,
  trackModifyHandler: (e: { target: unknown }) => void
) => {
  return (e: { target: unknown }) => {
    if (isMeasurementLine(e.target))
      updateMeasurementOnModify(canvas, e.target as FabricMeasurementLine);
    trackModifyHandler(e);
  };
};

export const createMeasurementMovementHandler = (canvas: Canvas) => {
  return (e: { target: unknown }) => {
    if (isMeasurementLine(e.target))
      updateMeasurementOnModify(canvas, e.target as FabricMeasurementLine);
  };
};

export const setupCanvasEventListeners = (
  canvas: Canvas,
  undoTracking: UndoTrackingState,
  objectStates: React.MutableRefObject<Map<string, FabricObjectState>>
): (() => void)[] => {
  const trackAddHandler = createUndoTrackingAddHandler(undoTracking, canvas);
  const trackRemoveHandler = createUndoTrackingRemoveHandler(undoTracking);
  const trackModifyHandler = createUndoTrackingModifyHandler(undoTracking, objectStates);
  const startMeasurementHandler = createObjectStateTrackingHandler(undoTracking, objectStates);
  const modifyMeasurementHandler = createMeasurementModificationHandler(canvas, trackModifyHandler);
  const moveMeasurementHandler = createMeasurementMovementHandler(canvas);

  return [
    canvas.on("path:created", (e) => {
      if (e.path) {
        // Handle flip adjustment for freehand paths
        const canvasElement = canvas.getElement();
        const currentTransform = canvasElement.style.transform || "";
        const flipState = {
          isFlippedX: currentTransform.includes("scaleX(-1)"),
          isFlippedY: currentTransform.includes("scaleY(-1)"),
        };
        const centerX = canvas.getWidth() / 2;
        const centerY = canvas.getHeight() / 2;

        if (flipState.isFlippedX) {
          const objCenter = e.path.getCenterPoint();
          const flippedCenterX = centerX - (objCenter.x - centerX);
          e.path.set({
            left:
              (e.path as any).originX === "center"
                ? flippedCenterX
                : flippedCenterX - e.path.getScaledWidth() / 2,
          });
          e.path.set({ scaleX: -(e.path.scaleX || 1) });
        }

        if (flipState.isFlippedY) {
          const objCenter = e.path.getCenterPoint();
          const flippedCenterY = centerY - (objCenter.y - centerY);
          e.path.set({
            top:
              (e.path as any).originY === "center"
                ? flippedCenterY
                : flippedCenterY - e.path.getScaledHeight() / 2,
          });
          e.path.set({ scaleY: -(e.path.scaleY || 1) });
        }
      }
      trackAddHandler(e);
    }),
    canvas.on("object:added", (e) => {
      if (e.target && e.target.type !== "path") {
        const objName = (e.target as any).name;
        if (!objName || (!objName.includes("background") && objName !== "cropRect")) {
          const canvasElement = canvas.getElement();
          const currentTransform = canvasElement.style.transform || "";
          const flipState = {
            isFlippedX: currentTransform.includes("scaleX(-1)"),
            isFlippedY: currentTransform.includes("scaleY(-1)"),
          };
          const centerX = canvas.getWidth() / 2;
          const centerY = canvas.getHeight() / 2;

          if (flipState.isFlippedX) {
            const objCenter = e.target.getCenterPoint();
            const flippedCenterX = centerX - (objCenter.x - centerX);
            e.target.set({
              left:
                (e.target as any).originX === "center"
                  ? flippedCenterX
                  : flippedCenterX - e.target.getScaledWidth() / 2,
            });

            // Apply flip to textbox directly or to group containing textbox
            if (e.target.type === "textbox" || (e.target as any).name === "measurementText") {
              e.target.set({ scaleX: -(e.target.scaleX || 1) });
            } else if (e.target.type === "group") {
              // For groups containing textboxes, apply flip to the group
              const group = e.target as any;
              const hasTextbox = group.getObjects().some((obj: any) => obj.type === "textbox");
              if (hasTextbox) {
                e.target.set({ scaleX: -(e.target.scaleX || 1) });
              }
            }
          }

          if (flipState.isFlippedY) {
            const objCenter = e.target.getCenterPoint();
            const flippedCenterY = centerY - (objCenter.y - centerY);
            e.target.set({
              top:
                (e.target as any).originY === "center"
                  ? flippedCenterY
                  : flippedCenterY - e.target.getScaledHeight() / 2,
            });

            // Apply flip to textbox directly or to group containing textbox
            if (e.target.type == "textbox" || (e.target as any).name === "measurementText") {
              e.target.set({ scaleY: -(e.target.scaleY || 1) });
            } else if (e.target.type === "group") {
              // For groups containing textboxes, apply flip to the group
              const group = e.target as any;
              const hasTextbox = group.getObjects().some((obj: any) => obj.type === "textbox");
              if (hasTextbox) {
                e.target.set({ scaleY: -(e.target.scaleY || 1) });
              }
            }
          }
        }
      }
      trackAddHandler(e);
    }),
    canvas.on("object:removed", trackRemoveHandler),
    canvas.on("object:moving", (e) => {
      startMeasurementHandler(e);
      moveMeasurementHandler(e);
    }),
    canvas.on("object:scaling", (e) => {
      startMeasurementHandler(e);
      moveMeasurementHandler(e);
    }),
    canvas.on("object:rotating", (e) => {
      startMeasurementHandler(e);
      moveMeasurementHandler(e);
    }),
    canvas.on("object:modified", modifyMeasurementHandler),
  ];
};
