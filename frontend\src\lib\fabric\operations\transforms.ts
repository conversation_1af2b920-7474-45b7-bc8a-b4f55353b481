import { Canvas, Point } from "fabric";
import { TransformState } from "@/shared/types";

export const applyCanvasRotation = (canvas: Canvas, onRotationComplete?: () => void): void => {
  const currentWidth = canvas.getWidth();
  const currentHeight = canvas.getHeight();

  canvas.setDimensions({
    width: currentHeight,
    height: currentWidth,
  });

  if (canvas.backgroundImage) {
    const bgImage = canvas.backgroundImage;
    const currentAngle = bgImage.angle || 0;
    const newAngle = (currentAngle + 90) % 360;

    bgImage.set({
      angle: newAngle,
      originX: "center",
      originY: "center",
      left: canvas.width! / 2,
      top: canvas.height! / 2,
    });
  }

  const oldCenter = new Point(currentWidth / 2, currentHeight / 2);
  const newCenter = new Point(currentHeight / 2, currentWidth / 2);

  canvas.getObjects().forEach((obj) => {
    const objCenter = obj.getCenterPoint();

    const dx = objCenter.x - oldCenter.x;
    const dy = objCenter.y - oldCenter.y;

    const newX = newCenter.x - dy;
    const newY = newCenter.y + dx;

    obj.set({
      left: newX,
      top: newY,
      angle: (obj.angle || 0) + 90,
      originX: "center",
      originY: "center",
    });

    obj.setCoords();
  });

  canvas.requestRenderAll();
  if (onRotationComplete) {
    onRotationComplete();
  }
};

export const applyCanvasFlipHorizontal = (canvas: Canvas, rotations: number = 0): void => {
  const effectiveRotations = rotations % 4;
  const shouldFlipX = effectiveRotations % 2 === 0;

  const canvasElement = canvas.getElement();
  const currentTransform = canvasElement.style.transform || "";

  if (shouldFlipX) {
    if (currentTransform.includes("scaleX(-1)")) {
      canvasElement.style.transform = currentTransform.replace("scaleX(-1)", "scaleX(1)");
    } else {
      canvasElement.style.transform = currentTransform + " scaleX(-1)";
    }
  } else {
    if (currentTransform.includes("scaleY(-1)")) {
      canvasElement.style.transform = currentTransform.replace("scaleY(-1)", "scaleY(1)");
    } else {
      canvasElement.style.transform = currentTransform + " scaleY(-1)";
    }
  }

  canvas.renderAll();
};

export const applyCanvasFlipVertical = (canvas: Canvas, rotations: number = 0): void => {
  const effectiveRotations = rotations % 4;
  const shouldFlipY = effectiveRotations % 2 === 0;

  const canvasElement = canvas.getElement();
  const currentTransform = canvasElement.style.transform || "";

  if (shouldFlipY) {
    if (currentTransform.includes("scaleY(-1)")) {
      canvasElement.style.transform = currentTransform.replace("scaleY(-1)", "scaleY(1)");
    } else {
      canvasElement.style.transform = currentTransform + " scaleY(-1)";
    }
  } else {
    if (currentTransform.includes("scaleX(-1)")) {
      canvasElement.style.transform = currentTransform.replace("scaleX(-1)", "scaleX(1)");
    } else {
      canvasElement.style.transform = currentTransform + " scaleX(-1)";
    }
  }

  canvas.renderAll();
};

export const getCanvasFlipState = (canvas: Canvas) => {
  const canvasElement = canvas.getElement();
  const currentTransform = canvasElement.style.transform || "";

  return {
    isFlippedX: currentTransform.includes("scaleX(-1)"),
    isFlippedY: currentTransform.includes("scaleY(-1)"),
  };
};

export const createRotateHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>,
  onRotationComplete?: () => void
) => {
  return () => {
    if (fabricCanvas?.current) {
      applyCanvasRotation(fabricCanvas.current, onRotationComplete);
      setTransformState((prev) => ({ ...prev, rotations: (prev.rotations + 1) % 4 }));
    }
  };
};

export const createFlipHorizontalHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>,
  getCurrentTransformState?: () => TransformState
) => {
  return () => {
    if (fabricCanvas?.current) {
      const currentState = getCurrentTransformState?.() || {
        rotations: 0,
        flipHorizontal: false,
        flipVertical: false,
      };
      applyCanvasFlipHorizontal(fabricCanvas.current, currentState.rotations);
      setTransformState((prev) => ({ ...prev, flipHorizontal: !prev.flipHorizontal }));
    }
  };
};

export const createFlipVerticalHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>,
  getCurrentTransformState?: () => TransformState
) => {
  return () => {
    if (fabricCanvas?.current) {
      const currentState = getCurrentTransformState?.() || {
        rotations: 0,
        flipHorizontal: false,
        flipVertical: false,
      };
      applyCanvasFlipVertical(fabricCanvas.current, currentState.rotations);
      setTransformState((prev) => ({ ...prev, flipVertical: !prev.flipVertical }));
    }
  };
};
