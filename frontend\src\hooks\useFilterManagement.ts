import { useCallback, useState, useMemo } from "react";
import { Canvas } from "fabric";
import {
  createB<PERSON>ness<PERSON><PERSON><PERSON>,
  create<PERSON><PERSON>rast<PERSON><PERSON><PERSON>,
  create<PERSON><PERSON><PERSON>le<PERSON><PERSON><PERSON>,
  createIn<PERSON><PERSON><PERSON><PERSON>,
  create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  create<PERSON>ammaR<PERSON><PERSON><PERSON>,
  create<PERSON>ammaGHand<PERSON>,
  createGammaBHandler,
} from "@/lib/fabric/operations/filters";
import type { FilterState, FilterManagementState, FabricConfig } from "@/shared/types";

export const useFilterManagement = (
  fabricConfigs: FabricConfig,
  fabricCanvas: React.RefObject<Canvas | null>
): FilterManagementState => {
  const initialFilters: FilterState = {
    brightness: fabricConfigs.brightness,
    contrast: fabricConfigs.contrast,
    grayscale: fabricConfigs.grayscale,
    invert: fabricConfigs.invert,
    sharpness: fabricConfigs.sharpness,
    gammaR: fabricConfigs.gammaR,
    gammaG: fabricConfigs.gammaG,
    gammaB: fabricConfigs.gammaB,
  };

  const [filters, setFilters] = useState<FilterState>(initialFilters);

  const updateFilter = useCallback((keyOrConfig: string | object, value?: number | boolean) => {
    if (typeof keyOrConfig === "object") {
      setFilters((prev) => ({ ...prev, ...keyOrConfig }));
    } else if (value !== undefined) {
      setFilters((prev) => ({ ...prev, [keyOrConfig]: value }));
    }
  }, []);

  const filterHandlers = useMemo(() => {
    const numericUpdateFilter = (key: string, value: number) => updateFilter(key, value);
    const booleanUpdateFilter = (key: string, value: boolean) => updateFilter(key, value);

    return {
      handleBrightnessChange: createBrightnessHandler(fabricCanvas, numericUpdateFilter),
      handleContrastChange: createContrastHandler(fabricCanvas, numericUpdateFilter),
      handleGrayscaleChange: createGrayscaleHandler(fabricCanvas, booleanUpdateFilter),
      handleInvertChange: createInvertHandler(fabricCanvas, booleanUpdateFilter),
      handleSharpnessChange: createSharpnessHandler(fabricCanvas, numericUpdateFilter),
      handleGammaRChange: createGammaRHandler(fabricCanvas, numericUpdateFilter),
      handleGammaGChange: createGammaGHandler(fabricCanvas, numericUpdateFilter),
      handleGammaBChange: createGammaBHandler(fabricCanvas, numericUpdateFilter),
    };
  }, [updateFilter, fabricCanvas]);

  return {
    filters,
    filterHandlers,
    updateFilter,
  };
};
